package lessonList

import (
	"deskcrm/api/dal"
	"deskcrm/controllers/http/innerapi/input/inputKeepDetail"
	"deskcrm/helpers"
	"deskcrm/service/arkBase/dataQuery"
	struArk "deskcrm/stru/ark"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"net/http"
	"net/http/httptest"
	"path"
	"runtime"
	"sync"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../../../..")

	helpers.PreInit()
	helpers.InitValidator()
	helpers.InitApiClient()
	helpers.InitRedis()
}

// testLessonListService 测试专用的服务结构体，重写了一些方法以避免外部依赖
type testLessonListService struct {
	*lessonListService
}

// 重写 GetLessonList 方法，简化逻辑以便测试
func (s *testLessonListService) GetLessonList(ctx *gin.Context) (lessonDataList *LessonListData, err error) {
	output, _ := s.initOutput(ctx, s.lessonIds)
	for _, lessonID := range s.lessonIds {
		if lesson, ok := s.lessonMap[lessonID]; ok {
			output.LessonListOutput[lessonID] = map[string]interface{}{
				"lessonId":   lessonID,
				"courseId":   s.param.CourseId,
				"startTime":  lesson.StartTime,
				"lessonName": lesson.LessonName,
				"status":     lesson.Status,
			}
		}
	}

	lessonList, err := s.outputToSliceAndSort(ctx, output)
	if err != nil {
		return nil, err
	}

	// 分页处理
	offset, limit := s.param.Offset, s.param.Limit
	total := len(lessonList)
	end := min(int(offset+limit), total)
	if int(offset) > total {
		lessonList = []map[string]interface{}{}
	} else {
		lessonList = lessonList[offset:end]
	}

	return &LessonListData{
		LessonList: lessonList,
		lessonIDs:  s.lessonIds,
		Total:      len(s.lessonMap),
	}, nil
}

func (s *testLessonListService) InitLessonListConfig(ctx *gin.Context, rules []*struLessonList.LessonRuleConfigStru) (err error) {
	keys := make([]string, 0)
	fieldRuleMap := map[string]*struLessonList.LessonRuleConfigStru{}
	for _, ruleConfig := range rules {
		keys = append(keys, ruleConfig.Key)
		fieldRuleMap[ruleConfig.Key] = ruleConfig
	}

	s.lock.Lock()
	s.fieldRuleMap = fieldRuleMap
	s.allFilterKeys = keys
	s.GroupKey = s.param.Tab
	s.lock.Unlock()
	return
}

// createTestLessonListService 创建测试用的 testLessonListService 实例
func createTestLessonListService() *testLessonListService {
	baseService := &lessonListService{
		param:              &inputKeepDetail.LessonListParam{},
		lessonIds:          make([]int64, 0),
		lessonMap:          map[int64]*dal.LessonInfo{},
		fieldRuleMap:       map[string]*struLessonList.LessonRuleConfigStru{},
		implodeStudentUids: make([]int64, 0),
		sorts:              make([]*struArk.SortsRule, 0),
		lock:               &sync.Mutex{},
	}
	return &testLessonListService{
		lessonListService: baseService,
	}
}

func createCtx() *gin.Context {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	req, _ := http.NewRequest("GET", "/", nil)
	req.AddCookie(&http.Cookie{
		Name:  "ZYBIPSCAS",
		Value: "IPS_e13a2d2419c79f6819022eeca4f6a2f01754639315",
	})
	ctx.Request = req
	return ctx
}

func initService(ctx *gin.Context, param *inputKeepDetail.LessonListParam, rules []*struLessonList.LessonRuleConfigStru) (leesonListPoint *testLessonListService, err error) {
	// 创建服务实例
	leesonListPoint = createTestLessonListService()

	dataQueryPoint := dataQuery.New()
	err = leesonListPoint.InitDataQueryPoint(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	err = leesonListPoint.InitParam(ctx, param)
	if err != nil {
		return
	}

	err = leesonListPoint.InitLessonList(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	err = leesonListPoint.InitLessonListConfig(ctx, rules)
	if err != nil {
		return
	}

	return
}

func createCommonParam() *inputKeepDetail.LessonListParam {
	return &inputKeepDetail.LessonListParam{
		AssistantUid: 4635163083,
		PersonUid:    3000332147,
		CourseId:     3740621,
		StudentUid:   4452570490,
		LeadsId:      4452570490,
		Offset:       0,
		Limit:        100,
	}
}

func TestLessonListService_GetLessonList_lessonId(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "lessonId",
			Function: "GetLessonId",
		},
	}

	ctx := createCtx()
	param := createCommonParam()

	leesonListPoint, err := initService(ctx, param, rules)
	assert.NoError(t, err)

	lessonDataList, err := leesonListPoint.GetLessonList(ctx)
	assert.NoError(t, err)

	t.Logf("result: %+v", lessonDataList)
}
