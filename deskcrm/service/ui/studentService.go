package ui

import (
	"deskcrm/api/arkgo"
	"deskcrm/api/dal"
	"deskcrm/api/dat"
	"deskcrm/api/dataproxy"
	"deskcrm/api/dau"
	"deskcrm/api/mesh"
	"deskcrm/api/moat"
	"deskcrm/api/touchmisgo"
	"deskcrm/api/userprofile"
	"deskcrm/components"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/controllers/http/innerapi/input/inputArk"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/models"
	"deskcrm/service/innerapi/survey"
	"deskcrm/stru/keepDetail"
	"deskcrm/util"
	"fmt"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/assistantdeskgo"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"math"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"deskcrm/service/ui/studentTagDefine"
	commonArkGo "git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type studentService struct{}

var (
	StudentService studentService
)

// 获取学生list
func (s studentService) GetStudentList(ctx *gin.Context, param *inputStudent.StudentListParam) (rsp outputStudent.StudentListOutput, err error) {
	// param
	if param == nil {
		err = components.InvalidParam("param is nil")
		return
	}

	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetStudentList error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetStudentList panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	// query: 方舟
	rspData, err := arkgo.NewClient().PostForwardRequest(ctx, arkgo.ArkListApi, components.StructToMap(ctx, transformParm4ArkList(param)))
	if err != nil || rspData == nil {
		return
	}

	var arkList arkgo.ArkListRsp
	err = json.Unmarshal([]byte(cast.ToString(rspData)), &arkList)
	if err != nil || arkList.Total == 0 {
		return outputStudent.StudentListOutput{}, err
	}
	// query: 学生list
	studentList, err := getBaseInfo(ctx, *param)
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}
	components.Debugf(ctx, "GetStudentList getBaseInfo,%+v,%+v", param, studentList)

	studentList.List = arkList.StudentList
	// studentList.FieldMapTree = arkList.FieldMapTree 此字段已废弃，由 getFieldMapTree 接口单独返回
	studentList.Total = arkList.Total
	studentList.SelectedCnt = arkList.FilterStudentCnt
	studentList.AllStudentsIDs = arkList.FilterStudentUids
	return studentList, nil
}

// 获取返回的基础信息, course、teacher等信息
func getBaseInfo(ctx *gin.Context, param inputStudent.StudentListParam) (outputStudent.StudentListOutput, error) {
	// param
	assistantUid := param.AssistantUid
	courseId := param.CourseId
	strCourseId := cast.ToString(courseId)
	lessonId := param.LessonId

	// query: dal, 获取课程章节信息
	id2Info, err := dal.GetCourseLessonInfoByCourseIds(ctx, []int64{courseId})
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}
	courseInfo, exist := id2Info[strconv.FormatInt(courseId, 10)]
	if !exist {
		return outputStudent.StudentListOutput{}, fmt.Errorf("课程信息不存在")
	}

	// 找到课程下的章节信息
	var lessonInfo dal.LessonInfo
	if lessonId > 0 && len(courseInfo.LessonList) > 0 {
		for _, info := range courseInfo.LessonList {
			if info.LessonId == int(lessonId) { // lessonId定义各处不统一
				lessonInfo = info
				break
			}
		}
	}
	// 获取辅导老师信息
	assistantInfo, err := mesh.NewClient().GetUserInfoByDeviceUid(ctx, assistantUid)
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}

	// 获取主讲老师name
	courseId2teacherNameList, err := GetTeacherNameListByCourseIds(ctx, []int64{courseId}, []string{"teacherName"})
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}

	// 返回数据
	var res outputStudent.StudentListOutput
	res.AssistantUID = assistantUid
	res.CourseName = courseInfo.CourseName
	res.CourseType = courseInfo.CourseType
	res.OnlineTime = courseInfo.OnlineFormatTimeAll
	res.TeacherName = strings.Join(courseId2teacherNameList[strCourseId], consts.SeparatorComma)
	res.AssistantPhone = assistantInfo.Record.Phone
	if util.IsHitGray(ctx, param.PersonUid, util.HideSensitivePhoneMercuryKey) {
		res.AssistantPhone = components.Util.HiddenPhone(assistantInfo.Record.Phone)
	}

	xbId := define.Grade2XB[int(courseInfo.MainGradeId)] // 学部id
	res.GradeStage = xbId

	var isLessonFinish int
	if lessonInfo.StopTime > 0 && int64(lessonInfo.StopTime) <= time.Now().Unix() { // 已结束
		isLessonFinish = 1
	} else {
		isLessonFinish = 0
	}
	res.IsLessonFinish = isLessonFinish

	res.SubjectID = int(courseInfo.MainSubjectId) // 类型经常不统一, 以哪个为准?
	res.GradeID = int(courseInfo.MainGradeId)
	res.NewCourseType = int(courseInfo.NewCourseType)

	surveyQueCond := map[string]interface{}{
		"year":         courseInfo.Year,
		"season":       courseInfo.Season,
		"type":         survey.TYPE_1,
		"department":   xbId,
		"subject":      courseInfo.MainSubjectId,
		"grade":        courseInfo.MainGradeId,
		"assistantUid": assistantUid,
		"courseId":     courseId,
	}

	// 此处的问卷看来已经废弃了，最新的记录是2022年的，可以考虑下线
	res.QueID, err = survey.GetBindSurveyQueId(ctx, surveyQueCond) // 获取调查问卷id
	if err != nil {
		return res, err
	}
	return res, nil
}

// 转化为arkList参数
func transformParm4ArkList(param *inputStudent.StudentListParam) *inputArk.ArkListParam {
	if param == nil {
		return &inputArk.ArkListParam{}
	}
	// transform
	return &inputArk.ArkListParam{
		AssistantUid:    param.AssistantUid,
		PersonUid:       param.PersonUid,
		CourseId:        param.CourseId,
		LessonId:        param.LessonId,
		TplId:           param.TplId,
		TaskId:          param.TaskId,
		ServiceId:       param.ServiceId,
		Timestamp:       param.Timestamp,
		Keyword:         param.Keyword,
		Sorts:           param.Sorts,
		Filter:          param.Filter,
		DataRangeSelect: param.DataRangeSelect,
		Offset:          param.Pn * param.Rn,
		Limit:           param.Rn,
	}
}

// 根据课程id获取老师信息
// return: courseId => TeacherNameList
func GetTeacherNameListByCourseIds(ctx *gin.Context, courseIds []int64, fields []string) (map[string][]string, error) {
	if len(courseIds) == 0 {
		return nil, fmt.Errorf("param:[courseIds] is empty]")
	}
	// 获取teacherUid list
	courseId2teacherUidList, err := dat.GetTeachersByCourseIds(ctx, courseIds, fields)
	if err != nil {
		return nil, err
	}

	// 获取teacher信息
	teacherUidList := getTeacherUidList(courseId2teacherUidList) // 合并teacherUid list
	teacherUid2Info, err := dau.GetTeachersByUids(ctx, teacherUidList, fields)
	if err != nil {
		return nil, err
	}

	// 返回数据
	result := make(map[string][]string) // courseId => TeacherNameList
	for curCourseId, curTeacherUidList := range courseId2teacherUidList {
		var teacherNameList []string
		for _, teacherUid := range curTeacherUidList.TeacherUids {
			teacherNameList = append(teacherNameList, teacherUid2Info[teacherUid].TeacherName)
		}
		result[curCourseId] = teacherNameList
	}
	return result, nil
}

// 合并去重返回 teacherUid list
func getTeacherUidList(courseId2teacherUidList map[string]dat.TeacherUidList) []int {
	seen := make(map[int]bool) // 用于去重 teacherUid
	var teacherUidList []int

	// 遍历
	for _, curUidList := range courseId2teacherUidList {
		for _, uid := range curUidList.TeacherUids {
			// 若未出现, 则添加
			if !seen[uid] { // not exist
				seen[uid] = true
				teacherUidList = append(teacherUidList, uid)
			}
		}
	}
	return teacherUidList
}

func (s studentService) StudentDelaminationDifferenceListV1(ctx *gin.Context, param inputStudent.StudentDelaminationDifferenceListV1Param) (rsp outputStudent.StudentDelaminationDifferenceListV1Resp, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "StudentDelaminationDifferenceListV1 error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "StudentDelaminationDifferenceListV1 panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	rsp = outputStudent.StudentDelaminationDifferenceListV1Resp{}
	rsp.HasDelamination = 1

	// 1.课程范围校验,仅支持小学和初中学部
	courseLessonInfos, err := dal.GetCourseLessonInfoByCourseIdsAndFields(ctx, []int64{param.CourseId}, []string{"courseId", "mainGradeId"}, nil)
	if err != nil {
		return
	}
	if info, ok := courseLessonInfos[cast.ToString(param.CourseId)]; ok {
		if !components.Array.InArrayInt(consts.GetDepartmentIdByGradeId(info.MainGradeId), []int{define.GradeStagePrimary, define.GradeStageJunior, define.GradeStageSenior}) {
			rsp.HasDelamination = 0
			return
		}
	}

	// 2.获取字段列规则
	rules, err := commonArkGo.GetFieldRuleByApp(ctx, commonArkGo.GetFieldRuleByAppParam{App: consts.ARK_APP_DETAIL_DELAMINATION})
	if err != nil {
		return
	}

	allRule := make(map[string]*commonArkGo.RuleFieldStruct)
	if rules != nil {
		for key, rule := range *rules {
			if _, ok := param.KeysMap[key]; ok {
				allRule[key] = rule
			}
		}
	}
	allFields := s.getStudentDelaminationFields(ctx, allRule)

	// 3.获取有差异的结果
	diffList, err := s.getDifferenceList(ctx, param)
	if err != nil {
		return
	}
	if len(diffList) == 0 {
		return
	}

	rsp.Total = len(diffList)
	rsp.PageSize = param.PageSize
	rsp.TotalPage = int(math.Ceil(float64(rsp.Total) / float64(rsp.PageSize)))
	rsp.Page = int(math.Min(float64(param.Page), float64(rsp.TotalPage)))

	startIndex := (rsp.Page - 1) * rsp.PageSize
	endIndex := startIndex + rsp.PageSize

	if endIndex > len(diffList) {
		endIndex = len(diffList)
	}

	if startIndex < len(diffList) {
		diffList = diffList[startIndex:endIndex]
	}

	// 4. 获取详情
	detail := make([]outputStudent.DelaminationDifference, 0)
	for _, item := range diffList {
		var differenceDetail outputStudent.DelaminationDifference
		differenceDetail, err = s.getDifferenceDetail(ctx, param, item, allFields, allRule)
		if err != nil {
			return
		}
		detail = append(detail, differenceDetail)
	}

	rsp.DiffrenceList = detail

	return
}

func (s studentService) getStudentDelaminationFields(ctx *gin.Context, rules map[string]*commonArkGo.RuleFieldStruct) (rsp []string) {
	rsp = make([]string, 0)
	for _, rule := range rules {
		if len(rule.ServiceConfig) == 0 {
			continue
		}

		allFields := make([]string, 0)
		// 处理serviceConfig中的numerator和denominator
		if numerator, hasNumerator := rule.ServiceConfig["numerator"]; hasNumerator {
			if denominator, hasDenominator := rule.ServiceConfig["denominator"]; hasDenominator {
				numeratorArr := strings.Split(numerator, "+")
				denominatorArr := strings.Split(denominator, "+")

				if len(numeratorArr) == 0 || len(denominatorArr) == 0 {
					continue
				}

				// 合并数组
				allFields = append(allFields, numeratorArr...)
				allFields = append(allFields, denominatorArr...)
			}
		}

		// 处理serviceConfig中的data
		if data, hasData := rule.ServiceConfig["data"]; hasData {
			allFields = append(allFields, data)
		}

		// 将这个规则的字段添加到结果中
		rsp = append(rsp, allFields...)
	}
	constArr := []string{"userDelaminationContinueLevel", "isPreclass", "sameTypePv", "sameTypeRank", "studentUid", "userBedType"}

	rsp = components.Array.StringArrUnique(append(rsp, constArr...))
	return
}

func (s studentService) getDifferenceList(ctx *gin.Context, input inputStudent.StudentDelaminationDifferenceListV1Param) (resp []keepDetail.Difference, err error) {
	resp = make([]keepDetail.Difference, 0)

	param := dataproxy.GetStudentLatestDelaminationDataByCAReq{
		CourseId:     input.CourseId,
		StudentUid:   input.StudentUid,
		AssistantUid: input.AssistantUid,
		Fields:       []string{"userDelaminationContinueLevel", "saveTime"},
	}

	studentDataList, err := dataproxy.NewClient().GetStudentLatestDelaminationDataByCA(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentLatestDelaminationDataByCA failed: %s", err.Error())
		return
	}

	sort.Slice(studentDataList, func(i, j int) bool {
		return studentDataList[i].SaveTime > studentDataList[j].SaveTime
	})

	// 上一条的分层值
	preUserDelaminationValue := 0

	for _, item := range studentDataList {
		userDelaminationValue := consts.Bed2Value[item.UserDelaminationContinueLevel]
		if userDelaminationValue == 0 {
			continue
		}

		// 获取第一条有效数据
		if preUserDelaminationValue == 0 {
			resp = append(resp, keepDetail.Difference{
				SaveTime:                      item.SaveTime,
				UserDelaminationContinueLevel: item.UserDelaminationContinueLevel,
				Class:                         "",
			})
			preUserDelaminationValue = userDelaminationValue
			continue
		}

		// change
		if userDelaminationValue > preUserDelaminationValue {
			resp = append(resp, keepDetail.Difference{
				SaveTime:                      item.SaveTime,
				UserDelaminationContinueLevel: item.UserDelaminationContinueLevel,
				Class:                         "down",
			})
			preUserDelaminationValue = userDelaminationValue
			continue
		}

		if userDelaminationValue < preUserDelaminationValue {
			resp = append(resp, keepDetail.Difference{
				SaveTime:                      item.SaveTime,
				UserDelaminationContinueLevel: item.UserDelaminationContinueLevel,
				Class:                         "top",
			})
			preUserDelaminationValue = userDelaminationValue
			continue
		}
	}

	return
}

func (s studentService) getDifferenceDetail(ctx *gin.Context, input inputStudent.StudentDelaminationDifferenceListV1Param,
	diff keepDetail.Difference, allFields []string, allRule map[string]*commonArkGo.RuleFieldStruct) (resp outputStudent.DelaminationDifference, err error) {
	resp = outputStudent.DelaminationDifference{}

	param := dataproxy.GetListByCourseIdAssistantUidSaveTimeReq{
		CourseId:     input.CourseId,
		StudentUid:   input.StudentUid,
		AssistantUid: input.AssistantUid,
		SaveTime:     diff.SaveTime,
		Fields:       allFields, // 不需要取这么多字段
	}

	// 获取基础数据
	list, err := dataproxy.NewClient().GetListByCourseIdAssistantUidSaveTime(ctx, param)
	if err != nil {
		return
	}
	resp.UserBedType = util.GetByDefault(list[input.StudentUid].UserDelaminationContinueLevel)
	resp.UserBedTypeHover = consts.UserBedTypeHover[list[input.StudentUid].UserBedType]
	resp.Model = ""
	if list[input.StudentUid].IsPreclass == 1 {
		resp.Model = "课前模型"
	}
	resp.ModelHover = ""
	if list[input.StudentUid].IsPreclass == 1 {
		resp.ModelHover = consts.PreClassHover
	}
	resp.Class = diff.Class
	t, _ := time.Parse("20060102", diff.SaveTime)
	resp.SaveTime = t.Format("2006-01-02")

	if list[input.StudentUid].SameTypePv > 0 {
		percentage := math.Round(float64((list[input.StudentUid].SameTypeRank/
			list[input.StudentUid].SameTypePv)*100*100)) / 100
		resp.UserBedRank = fmt.Sprintf("%.2f%%", percentage)
	} else {
		resp.UserBedRank = "0%"
	}

	needHandleRule := make(map[string]*commonArkGo.RuleFieldStruct)
	arkKeys := make([]string, 0)
	for key, rule := range allRule {
		if components.Util.FirstToUpper(rule.Function) == consts.DifferenceDetailFunc {
			needHandleRule[key] = rule
			arkKeys = append(arkKeys, key)
		}
	}

	// 方舟获取学生详细数据
	formatData, err := commonArkGo.ArkFormat(ctx, commonArkGo.ArkFormatReq{
		AssistantUid: input.AssistantUid,
		PersonUid:    input.PersonUid,
		CourseId:     input.CourseId,
		StudentUids:  []int64{input.StudentUid},
		FormatKeys:   arkKeys,
	})
	if err != nil {
		return outputStudent.DelaminationDifference{}, err
	}

	stuDataMap := make(map[string]interface{})
	for _, key := range arkKeys {
		stuDataMap[key] = formatData.StudentList[input.StudentUid][key]
	}

	// 字段数据分组
	config := make(map[string][]*commonArkGo.RuleFieldStruct)

	for _, rule := range allRule {
		if _, ok := rule.ServiceConfig["category"]; !ok {
			config[consts.CategoryUnknown] = append(config[consts.CategoryUnknown], rule)
			continue
		}
		config[rule.ServiceConfig["category"]] = append(config[rule.ServiceConfig["category"]], rule)
	}

	detail := make([]outputStudent.CategoryDetail, 0)
	for _, category := range consts.Categories {
		if _, ok := config[category]; !ok {
			continue
		}

		item := outputStudent.CategoryDetail{
			ClassName: category,
			List:      make([]outputStudent.Indicator, 0),
		}
		for _, rule := range config[category] {
			key := rule.Key
			if data, ok := stuDataMap[key]; !ok {
				continue
			} else {
				item.List = append(item.List, outputStudent.Indicator{
					Value:     rule.CustomName,
					Label:     data,
					Highlight: 0,
				})
			}
		}

		detail = append(detail, item)
	}
	resp.Detail = detail
	return
}

// GetWxBindInfo 获取微信绑定信息
func (s studentService) GetWxBindInfo(ctx *gin.Context, param *inputStudent.GetWxBindInfoParam) (rsp outputStudent.GetWxBindInfoOutput, err error) {
	// 初始化返回值
	rsp = outputStudent.GetWxBindInfoOutput{
		IsBind: 0,
		IsShow: 0,
	}

	// 检查是否显示微信按钮
	courseList, err := models.TblShowWxCourseDao.GetListByCourseId(ctx, param.CourseId, models.ShowWxCourseStatusValid)
	if err != nil {
		zlog.Warnf(ctx, "query show_wx_course failed, courseId:%d, err:%v", param.CourseId, err)
	}
	if len(courseList) > 0 {
		rsp.IsShow = 1
	}

	// 获取手动标记的微信绑定数据
	bindDataList, err := models.TblWechatBindTempEnterpriseDao.GetByUid(ctx, param.AssistantUid, param.StudentUid)
	if err != nil {
		zlog.Warnf(ctx, "get manual bind data failed, assistantUid:%d, studentUid:%d, err:%v",
			param.AssistantUid, param.StudentUid, err)
	}
	if len(bindDataList) > 0 {
		rsp.IsBind = bindDataList[0].IsBind
	}

	return rsp, nil
}

// GetDetailPageOption 获取学员详情页面选项
func (s studentService) GetDetailPageOption(ctx *gin.Context) (*outputStudent.GetDetailPageOptionResponse, error) {
	// 创建响应对象
	resp := outputStudent.NewGetDetailPageOptionResponse()
	return resp, nil
}

// GetCourseRecordDefaultOption 获取课程记录默认选项
func (s studentService) GetCourseRecordDefaultOption(ctx *gin.Context, param *inputStudent.CourseRecordDefaultOptionParam) (*outputStudent.CourseRecordDefaultOptionResp, error) {
	// 创建响应对象
	resp := &outputStudent.CourseRecordDefaultOptionResp{}

	// 1. 获取课程基本信息
	courseMap, err := dal.GetCourseBaseByCourseIds(ctx, []int64{int64(param.CourseId)}, []string{"season", "newCourseType", "year", "serviceInfo"})
	if err != nil {
		zlog.Errorf(ctx, "GetCourseBaseByCourseIds failed, courseId: %d, err: %v", param.CourseId, err)
		return resp, fmt.Errorf("获取课程信息失败: %w", err)
	}

	courseInfo, exists := courseMap[int64(param.CourseId)]
	if !exists {
		zlog.Warnf(ctx, "courseInfo not found, courseId: %d", param.CourseId)
		return resp, fmt.Errorf("课程信息不存在，课程ID: %d", param.CourseId)
	}

	// 2. 获取交易信息判断退款状态
	key := fmt.Sprintf("%d_%d", param.StudentUid, param.CourseId)
	tradeInfo, err := s.getTradeInfoByStudentCourse(ctx, key)
	if err != nil {
		zlog.Errorf(ctx, "getTradeInfoByStudentCourse failed, key: %s, err: %v", key, err)
	}

	// 3. 判断退款状态
	isRefund := consts.BuyAll // 默认状态
	if tradeInfo != nil {
		tradeStatus := tradeInfo.RefundStatus
		if tradeStatus == consts.TradeStatusRefunded || tradeStatus == consts.TradeStatusRefundPart {
			isRefund = consts.BuyRefund
		} else {
			isRefund = consts.BuyNormal
		}
	}

	// 4. 获取课程服务类型
	courseServiceTypeStr := s.getCourseServiceTypeByCourseInfo(ctx, courseInfo)

	// 5. 设置响应数据
	resp.Season = courseInfo.Season
	resp.NewCourseType = courseInfo.NewCourseType
	resp.IsRefund = isRefund
	resp.Year = courseInfo.Year
	resp.CourseServiceType = courseServiceTypeStr

	return resp, nil
}

// getTradeInfoByStudentCourse 根据学生和课程获取交易信息
func (s studentService) getTradeInfoByStudentCourse(ctx *gin.Context, key string) (*moat.DarKVByCourseIdsItem, error) {
	// 参数校验
	if key == "" {
		return nil, fmt.Errorf("key不能为空")
	}

	zlog.Infof(ctx, "getTradeInfoByStudentCourse called, key: %s", key)

	// 定义需要的字段
	fields := []string{
		"userId", "courseId", "tradeId", "subTradeId", "tradeFee", "tradeTime",
		"orderBusinessStatus", "refundStatus", "refundStartTime", "changeFromCourseId",
		"changeToCourseId", "changeTime", "createTime", "updateTime", "logInfo",
	}

	// 调用 DAR 服务获取交易信息
	tradeList, err := moat.NewClient().DarGetKVByCourseIds(ctx, []string{key}, fields)
	if err != nil {
		zlog.Errorf(ctx, "getTradeInfoByStudentCourse: DarGetKVByCourseIds failed, key: %s, err: %v", key, err)
		return nil, fmt.Errorf("获取交易信息失败: %w", err)
	}

	// 查找匹配的交易信息
	for _, tradeInfo := range tradeList {
		// 构建学生课程键进行匹配
		studentCourseKey := fmt.Sprintf("%d_%d", tradeInfo.UserId, tradeInfo.CourseId)
		if studentCourseKey == key {
			return &tradeInfo, nil
		}
	}

	zlog.Infof(ctx, "getTradeInfoByStudentCourse: no trade info found for key: %s", key)
	return nil, nil
}

// getCourseServiceTypeByCourseInfo 根据课程信息获取课程服务类型
func (s studentService) getCourseServiceTypeByCourseInfo(ctx *gin.Context, courseInfo dal.CourseInfo) string {
	// 检查服务信息列表，判断课程服务类型
	if len(courseInfo.ServiceInfo) == 0 {
		zlog.Warnf(ctx, "getCourseServiceTypeByCourseInfo: no serviceInfo found for courseId: %d", courseInfo.CourseId)
		return consts.CourseServiceTypeOther
	}

	// 提取所有服务ID
	serviceIds := make([]int64, 0, len(courseInfo.ServiceInfo))
	for _, service := range courseInfo.ServiceInfo {
		serviceIds = append(serviceIds, service.ServiceID)
	}

	// 检查是否包含督学服务 (serviceId 1164)
	if slices.Contains(serviceIds, consts.ServiceIdLX) {
		zlog.Infof(ctx, "getCourseServiceTypeByCourseInfo: found LX service for courseId: %d", courseInfo.CourseId)
		return consts.CourseServiceTypeLX
	}

	// 检查是否包含班主任服务 (serviceId 1160)
	if slices.Contains(serviceIds, consts.ServiceIdDXB) {
		zlog.Infof(ctx, "getCourseServiceTypeByCourseInfo: found DXB service for courseId: %d", courseInfo.CourseId)
		return consts.CourseServiceTypeDXB
	}

	// 其他情况返回其他课程类型
	zlog.Infof(ctx, "getCourseServiceTypeByCourseInfo: using OTHER service type for courseId: %d", courseInfo.CourseId)
	return consts.CourseServiceTypeOther
}

// GetStudentCallRecordInfo 获取学生通话记录信息
func (s studentService) GetStudentCallRecordInfo(ctx *gin.Context, param *inputStudent.GetStudentCallRecordInfoParam) (resp *outputStudent.GetStudentCallRecordInfoResp, err error) {
	resp = &outputStudent.GetStudentCallRecordInfoResp{
		CallList:       []outputStudent.CallInfo{},
		CallRecordList: []outputStudent.CallRecord{},
		CallCountInfo:  outputStudent.CallCountInfo{},
	}

	// 获取学生基本信息
	studentInfo, err := s.getStudentInfo(ctx, param.StudentUid)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo get student info failed, studentUid: %d, err: %v", param.StudentUid, err)
		return
	}

	// 获取学生电话列表
	callList, err := s.getStudentCallList(ctx, studentInfo)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo get call list failed, studentUid: %d, err: %v", param.StudentUid, err)
		return
	}

	// 如果指定了CourseId，添加收货电话信息
	if param.CourseId > 0 {
		addressInfo := s.getAddressInfo(ctx, param.StudentUid, param.CourseId)
		addressPhone := addressInfo.Phone
		if addressPhone != "" {
			// 电话号码脱敏
			maskedPhone := util.MaskPhone(addressPhone)
			// 电话号码MD5
			md5Phone := util.EncodePhone(addressPhone)

			addressCallInfo := outputStudent.CallInfo{
				Name:      "收货电话",
				Phone:     maskedPhone,
				Md5Phone:  md5Phone,
				City:      "",
				CityLevel: "",
			}
			callList = append(callList, addressCallInfo)
		}
	}

	// 获取通话记录
	callRecordList, callCountInfo, err := s.getStudentCallRecords(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo get call records failed, studentUid: %d, err: %v", param.StudentUid, err)
		return
	}

	if len(callRecordList) > 0 {
		resp.CallRecordList = callRecordList
	}
	if len(callList) > 0 {
		resp.CallList = callList
	}
	resp.CallCountInfo = callCountInfo

	return resp, nil
}

// getStudentInfo 获取学生基本信息
func (s studentService) getStudentInfo(ctx *gin.Context, studentUid int64) (*dau.StudentInfo, error) {
	// 调用 DAU 获取学生信息
	studentInfoMap, err := dau.GetStudents(ctx, []int64{studentUid}, []string{
		"studentUid", "studentName", "phone", "guardian", "guardianPhone", "registerPhone",
	})
	if err != nil {
		return nil, fmt.Errorf("get student info from DAU failed: %v", err)
	}

	studentInfo, exists := studentInfoMap[studentUid]
	if !exists {
		return nil, fmt.Errorf("student not found: %d", studentUid)
	}

	return studentInfo, nil
}

// getStudentCallList 获取学生可拨打电话列表
func (s studentService) getStudentCallList(ctx *gin.Context, studentInfo *dau.StudentInfo) ([]outputStudent.CallInfo, error) {
	var callList []outputStudent.CallInfo

	// 获取所有电话号码
	phones := s.getUniquePhones(studentInfo)

	for _, phoneInfo := range phones {
		if phoneInfo.Phone == "" {
			continue
		}

		// 电话号码脱敏
		maskedPhone := util.MaskPhone(phoneInfo.Phone)

		// 电话号码MD5
		md5Phone := util.EncodePhone(phoneInfo.Phone)

		// 获取城市信息
		city, cityLevel := util.GetPhoneLocation(ctx, phoneInfo.Phone)

		callInfo := outputStudent.CallInfo{
			Name:      phoneInfo.Name,
			Phone:     maskedPhone,
			Md5Phone:  md5Phone,
			City:      city,
			CityLevel: cityLevel,
		}

		callList = append(callList, callInfo)
	}

	return callList, nil
}

// phoneInfo 电话信息结构
type phoneInfo struct {
	Name     string
	Identity string
	Phone    string
}

// getUniquePhones 获取学生的唯一电话号码列表
func (s studentService) getUniquePhones(studentInfo *dau.StudentInfo) []phoneInfo {
	var phones []phoneInfo

	// 注册电话
	if studentInfo.RegisterPhone != "" {
		phones = append(phones, phoneInfo{
			Name:     "注册电话",
			Identity: "register",
			Phone:    studentInfo.RegisterPhone,
		})
	}

	// 学生电话
	if studentInfo.Phone != "" {
		phones = append(phones, phoneInfo{
			Name:     "学生电话",
			Identity: "student",
			Phone:    studentInfo.Phone,
		})
	}

	// 监护人电话
	if studentInfo.GuardianPhone != "" {
		phones = append(phones, phoneInfo{
			Name:     "监护人电话",
			Identity: "guardian",
			Phone:    studentInfo.GuardianPhone,
		})
	}

	return phones
}

// getStudentCallRecords 获取学生通话记录
func (s studentService) getStudentCallRecords(ctx *gin.Context, param *inputStudent.GetStudentCallRecordInfoParam) ([]outputStudent.CallRecord, outputStudent.CallCountInfo, error) {
	var callRecordList []outputStudent.CallRecord
	var callCountInfo outputStudent.CallCountInfo

	// 计算时间范围
	endTime := time.Now().Unix()
	var startTime int64
	if param.BacktraceTime > 0 {
		startTime = endTime - int64(param.BacktraceTime)
		// 对特定类型进行时间调整，参考PHP版本逻辑
		if param.Type == 1 || param.Type == consts.SourceType16 {
			startTime = startTime + 86400*6 // 加6天
			if startTime > endTime-86400 {
				zlog.Infof(ctx, "通话记录时间范围当前设置小于7天，请确认时间范围无误[%d]", param.BacktraceTime)
				startTime = endTime - 86400 // 最少1天
			}
		}
	} else {
		// 默认查询7天，与PHP版本保持一致
		startTime = endTime - 86400*7
		// 对特定类型默认查询1天
		if param.Type == 1 || param.Type == consts.SourceType16 {
			startTime = endTime - 86400
		}
	}

	// 调用 TouchMisGo 获取通话记录
	touchMisGoClient := touchmisgo.NewClient()
	callParams := touchmisgo.CallRecordListParams{
		ToUid:     []int64{param.StudentUid},
		StartTime: startTime,
		EndTime:   endTime,
		Pn:        0,
		Rn:        200, // 最多获取200条记录
	}

	if param.Type > 0 {
		callParams.SourceType = param.Type
	}

	touchResp, err := touchMisGoClient.CallRecordList(ctx, callParams)
	if err != nil {
		zlog.Warnf(ctx, "getStudentCallRecords call TouchMisGo failed, err: %v", err)
		return callRecordList, callCountInfo, nil // 不返回错误，返回空列表
	}

	// 收集所有的设备UID用于批量获取用户信息，优先使用deviceUid，如果为0则使用fromUid
	assistantUids := make([]int64, 0)
	assistantUidSet := make(map[int64]bool)
	for _, record := range touchResp.List {
		var uid int64
		if record.DeviceUid == 0 {
			// 外呼设备uid存储之前用fromUid
			uid = record.FromUid
		} else {
			uid = record.DeviceUid
		}

		if uid > 0 && !assistantUidSet[uid] {
			assistantUids = append(assistantUids, uid)
			assistantUidSet[uid] = true
		}
	}

	// 批量获取用户信息
	deviceInfoMap := make(map[int64]*userprofile.DeviceInfo)
	if len(assistantUids) > 0 {
		userprofileClient := userprofile.NewClient()
		var err error
		deviceInfoMap, err = userprofileClient.GetDeviceInfoList(ctx, assistantUids)
		if err != nil {
			zlog.Warnf(ctx, "getStudentCallRecords GetDeviceInfoList failed, err: %v", err)
		}
	}

	// 处理通话记录
	totalNum := 0
	successNum := 0

	for _, record := range touchResp.List {
		// 获取拨打人姓名 - 对应PHP版本的设备UID处理逻辑
		var callerUid int64
		if record.DeviceUid == 0 {
			// 外呼设备uid存储之前用fromUid
			callerUid = record.FromUid
		} else {
			callerUid = record.DeviceUid
		}
		callerName := s.getCallerNameFromMap(callerUid, deviceInfoMap)

		callRecord := outputStudent.CallRecord{
			Name:           callerName,
			StartTime:      time.Unix(record.CreateTime, 0).Format("01-02 15:04"), // 对应PHP版本的"m-d H:i"格式
			Duration:       record.Duration / 1000,                                // 转换为秒
			CallResult:     s.getCallResultText(record.CallResult),
			CallResultType: s.getCallResultType(record.CallResult),
			CallId:         record.CallId,
			CallMode:       s.getCallModeWithDefault(record.CallMode), // 对应PHP版本的默认值处理
			FromPhone:      util.MaskPhone(record.FromPhone),
			SourceTypeName: s.getSourceTypeName(record.SourceType),
			SourceType:     record.SourceType,
			SortTime:       record.CreateTime,
		}

		callRecordList = append(callRecordList, callRecord)

		// 统计通话次数
		totalNum++
		if record.CallResult == consts.CallResultConnected {
			successNum++
		}
	}

	// 按createTime降序排列，对应PHP版本的排序逻辑
	sort.Slice(callRecordList, func(i, j int) bool {
		return callRecordList[i].SortTime > callRecordList[j].SortTime
	})

	// 计算成功率
	successRate := 0.0
	if totalNum > 0 {
		successRate = float64(successNum) / float64(totalNum) * 100
	}

	callCountInfo = outputStudent.CallCountInfo{
		TotalNum:    totalNum,
		SuccessNum:  successNum,
		SuccessRate: successRate,
	}

	return callRecordList, callCountInfo, nil
}

// getCallerNameFromMap 从设备信息映射中获取拨打人姓名
// 对应PHP版本逻辑：只使用nickname字段，不存在时返回空字符串
func (s studentService) getCallerNameFromMap(fromUid int64, deviceInfoMap map[int64]*userprofile.DeviceInfo) string {
	// 从映射中获取设备信息，对应PHP版本的nickname字段处理
	if deviceInfo, exists := deviceInfoMap[fromUid]; exists && deviceInfo != nil {
		if deviceInfo.Nickname != "" {
			return deviceInfo.Nickname
		}
	}

	// 对应PHP版本：如果nickname不存在，返回空字符串
	return ""
}

// getCallResultText 获取通话结果文本
func (s studentService) getCallResultText(callResult int) string {
	if text, exists := consts.CallResultTextMap[callResult]; exists {
		return text
	}
	return "未知"
}

// getCallResultType 获取通话结果类型
func (s studentService) getCallResultType(callResult int) int {
	if callResult == consts.CallResultConnected {
		return 1 // 接通
	}
	return 0 // 未接通
}

// getSourceTypeName 获取来源类型名称
func (s studentService) getSourceTypeName(sourceType int) string {
	if name, exists := consts.SourceTypeNameMap[sourceType]; exists {
		return name
	}
	return ""
}

// getCallModeWithDefault 获取通话模式，对应PHP版本的默认值处理
func (s studentService) getCallModeWithDefault(callMode int) int {
	if callMode > 0 {
		return callMode
	}
	// 对应PHP版本：Api_Muse::CALLOUT_MODE_SHIELD = 9
	return consts.CalloutModeShield
}

// getAddressInfo 获取学生课程的收货电话
// 对应PHP版本的getstudentcourse方法
func (s studentService) getAddressInfo(ctx *gin.Context, studentUid, courseId int64) (res moat.DetailAddressInfo) {
	res = moat.DetailAddressInfo{}
	if studentUid <= 0 || courseId <= 0 {
		return
	}

	// 构造学员课程ID，格式：studentUid_courseId
	studentCourseId := fmt.Sprintf("%d_%d", studentUid, courseId)

	// 调用DAR API获取订单信息
	moatClient := moat.NewClient()
	darResp, err := moatClient.DarGetKVByCourseIds(ctx, []string{studentCourseId}, []string{"tradeId"})
	if err != nil {
		zlog.Warnf(ctx, "getAddressInfo DarGetKVByCourseIds failed, studentUid=%d, courseId=%d, err=%v", studentUid, courseId, err)
		return
	}

	if len(darResp) == 0 {
		zlog.Warnf(ctx, "getAddressInfo no trade found, studentUid=%d, courseId=%d", studentUid, courseId)
		return
	}

	tradeId := darResp[0].TradeId
	if tradeId <= 0 {
		zlog.Warnf(ctx, "getAddressInfo invalid tradeId, studentUid=%d, courseId=%d, tradeId=%d", studentUid, courseId, tradeId)
		return
	}

	// 调用订单详情API获取地址信息
	tradeResp, err := moatClient.GetTradeDetail(ctx, studentUid, []int64{tradeId}, []string{"addressInfo", "subList"})
	if err != nil {
		zlog.Warnf(ctx, "getAddressInfo GetTradeDetail failed, studentUid=%d, tradeId=%d, err=%v", studentUid, tradeId, err)
		return
	}

	if len(tradeResp) == 0 {
		zlog.Warnf(ctx, "getAddressInfo no trade detail found, studentUid=%d, tradeId=%d", studentUid, tradeId)
		return
	}

	// 从订单详情中提取收货电话
	tradeDetail := tradeResp[0]

	res = tradeDetail.AddressInfo
	return
}

func (s studentService) StudentDetailV1(ctx *gin.Context, param *inputStudent.StudentDetailV1Param) (resp *outputStudent.StudentDetailV1Resp, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "StudentDetailV1 error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "StudentDetailV1 panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()
	resp = &outputStudent.StudentDetailV1Resp{
		Student: outputStudent.StudentInfo{},
	}
	// 1,方舟获取相关数据
	studentArkData, err := s.initArkStudentData(ctx, param)
	if err != nil {
		return
	}

	// 2,基本信息获取
	resp, err = s.getStudentDetailBaseInfo(ctx, param, studentArkData)
	if err != nil {
		return
	}

	// 3，tag 标签 format
	tags, err := s.formatTags(ctx, param, studentArkData)
	if err != nil {
		return
	}
	resp.Tags = tags
	return
}

func (s studentService) formatTags(ctx *gin.Context, param *inputStudent.StudentDetailV1Param, arkData map[string]interface{}) (resp []outputStudent.Tag, err error) {
	resp = make([]outputStudent.Tag, 0)
	if len(param.TagArr) == 0 {
		return
	}

	rules, err := commonArkGo.GetFieldRuleByKeys(ctx, commonArkGo.GetFieldRuleByKeysParam{Keys: param.TagArr})
	if err != nil || rules == nil {
		return
	}
	rulesTemp := *rules

	for _, tagKey := range param.TagArr {
		// 特殊样式处理
		if _, ok := studentTagDefine.TagDIYStyleMap[tagKey]; ok {
			resp = append(resp, s.formatDIYTags(ctx, tagKey, arkData)...)
			continue
		}

		// tag 样式定义
		style := studentTagDefine.GetTagStyle(tagKey)

		// 无数据不返回
		if _, ok := arkData[tagKey]; !ok {
			continue
		}

		// 配置获取是否展示原始值/FilterMap 映射值
		_, showOriginal := studentTagDefine.ShowValueInOriginal[tagKey]
		if _, exits := rulesTemp[tagKey]; !exits && !showOriginal {
			continue
		}

		// 非展示原始值 && 无映射，不展示
		if _, inFilterMap := rulesTemp[tagKey].FilterMap[cast.ToString(arkData[tagKey])]; !inFilterMap && !showOriginal {
			continue
		}

		if showOriginal {
			style.Label = cast.ToString(arkData[tagKey])
		} else {
			style.Label = rulesTemp[tagKey].FilterMap[cast.ToString(arkData[tagKey])]
		}

		resp = append(resp, style)
	}
	return
}

func (s studentService) formatDIYTags(ctx *gin.Context, key string, arkData map[string]interface{}) (resp []outputStudent.Tag) {
	resp = make([]outputStudent.Tag, 0)
	switch key {
	case consts.TagUserType:
		if style, ok := studentTagDefine.TagUserTypeStyleMap[consts.TagUserType][cast.ToString(arkData["userType"])]; ok {
			resp = append(resp, style)
		}
	case consts.TagLpcUserType:
		if style, ok := studentTagDefine.TagUserTypeStyleMap[consts.TagLpcUserType][cast.ToString(arkData[consts.TagLpcUserType])]; ok {
			resp = append(resp, style)
		}
	case consts.TagIsBound, consts.TagSingleApplyRetain:
		hover := ""
		if arkData["isBoundHover"] == 1 {
			hover = "学员续报且联报下两期课程"
		}
		if arkData["isBoundHover"] == 2 {
			hover = "学员通过联报报名当前学季与下一级课程"
		}
		if hover != "" {
			style := studentTagDefine.DetailTagIsBound
			style.Hover = hover
			resp = append(resp, style)
		}

		if arkData["singleApplyRetain"] == 1 {
			resp = append(resp, studentTagDefine.DetailTagSingleApplyRetain)
		}
	case consts.TagIsLevelTwo:
		if cast.ToInt64(arkData["isLevelTwo"]) > 0 {
			resp = append(resp, studentTagDefine.DetailTagIsLevelTwo)
		}
	case consts.TagIsTransferCourse:
		if cast.ToInt64(arkData["isTransferCourse"]) == 1 {
			style := studentTagDefine.DetailTagIsTransferCourse
			style.Hover = cast.ToString(arkData["isTransferCourseHover"])
			resp = append(resp, studentTagDefine.DetailTagIsTransferCourse)
		}
	case consts.TagIsTransferStudent:
		if cast.ToInt64(arkData["isTransferStudent"]) == 1 {
			resp = append(resp, studentTagDefine.DetailTagIsTransferStudent)
		}
	case consts.TagIsTransferStudentB:
		if cast.ToInt64(arkData["isTransferStudentB"]) == 1 {
			resp = append(resp, studentTagDefine.DetailTagIsTransferStudentB)
		}
	case consts.TagDelaySale:
		if cast.ToInt64(arkData["tagDelaySale"]) == 1 {
			style := studentTagDefine.DetailTagDelaySale
			resp = append(resp, style)
		}
	case consts.TagGuardianWechatLight:
		resp = append(resp, studentTagDefine.DetailTagGuardianWechatLight)
	case consts.TagServiceType:
		label := "未关注或未绑定"
		if cast.ToInt64(arkData["serviceType"]) == 1 {
			label = "服务号关注且绑定"
		}
		style := studentTagDefine.DetailTagServiceType
		style.Label = label
		resp = append(resp, style)
	case consts.TagChannel:
		if d, ok := consts.LeadsFirstChannelIDMap[cast.ToInt(arkData["tagChannel"])]; ok {
			style := studentTagDefine.DetailTagChannel
			style.Label = d
			resp = append(resp, style)
		}
	case consts.TagLpcNum:
		if cast.ToInt64(arkData["tagLpcNum"]) > 1 {
			style := studentTagDefine.DetailTagLpcNum
			style.Label = "LPCx" + cast.ToString(arkData["tagLpcNum"])
			resp = append(resp, style)
		}
	case consts.TagLpcLeadsDeploy:
		if cast.ToInt64(arkData["tagLpcLeadsDeploy"]) > 0 {
			style := studentTagDefine.DetailTagLpcLeadsDeploy
			style.Label = "调配"
			resp = append(resp, style)
		}
	case consts.TagLpcSecondChannel:
		if data, ok := arkData["secondaryChannel"]; ok && len(cast.ToString(data)) > 0 {
			var secondaryChannel keepDetail.StudentSecondaryChannel
			err := json.Unmarshal([]byte(cast.ToString(data)), &secondaryChannel)
			if err != nil {
				zlog.Warnf(ctx, "formatDIYTags json.Unmarshal StudentSecondaryChannel failed, err:%v", err)
				break
			}
			if secondaryChannel.Level != "" {
				style := studentTagDefine.DetailTagLpcSecondChannel
				style.Label = "二级渠道·" + cast.ToString(secondaryChannel.Level) + "(" + cast.ToString(secondaryChannel.Remark) + ")"
				resp = append(resp, style)
			}
		}
	case consts.TagLpcCityLevel:
		if data, ok := arkData["cityInfo"]; ok && len(cast.ToString(data)) > 0 {
			var cityInfo keepDetail.StudentCityLevel
			err := json.Unmarshal([]byte(cast.ToString(data)), &cityInfo)
			if err != nil {
				zlog.Warnf(ctx, "formatDIYTags json.Unmarshal StudentSecondaryChannel failed, err:%v", err)
				break
			}
			if cityInfo.Level != "" {
				style := studentTagDefine.DetailTagLpcCityLevel
				style.Label = cast.ToString(cityInfo.Level)
				resp = append(resp, style)
			}
		}
	case consts.TagLpcTransferStatus:
		trans := "未转化"
		if cast.ToInt64(arkData["tagLpcTransferStatus"]) == 2 {
			trans = "已转化"
		}
		style := studentTagDefine.DetailTagLpcTranseStatus
		style.Label = trans
		resp = append(resp, style)
	case consts.TagLpcUserDelamination:
		if cast.ToInt64(arkData["studentLayerTag"]) > 0 {
			style := studentTagDefine.DetailTagLpcUserDelamination
			style.Label = cast.ToString(arkData["studentLayerTag"])
			resp = append(resp, style)
		}
	case consts.TagLpcChannel:
		if d, ok := consts.LeadsSourceMap[cast.ToInt(arkData["leadsSource"])]; ok {
			style := studentTagDefine.DetailTagLpcChannel
			style.Label = d
			resp = append(resp, style)
		}
	}

	return
}

func (s studentService) initArkStudentData(ctx *gin.Context, param *inputStudent.StudentDetailV1Param) (resp map[string]interface{}, err error) {
	resp = make(map[string]interface{})

	arkKeys := append(consts.StudentDetailArkDefaultKeys, studentTagDefine.ReplaceTagArkKey(param.TagArr)...)

	formatData, err := commonArkGo.ArkFormat(ctx, commonArkGo.ArkFormatReq{
		AssistantUid:         param.AssistantUid,
		PersonUid:            param.PersonUid,
		CourseId:             param.CourseId,
		StudentUids:          []int64{param.StudentUid},
		LeadsIdMapStudentUid: map[int64]int64{param.LeadsId: param.StudentUid},
		FormatKeys:           arkKeys,
	})
	if err != nil {
		return
	}

	resp = formatData.StudentList[param.StudentUid]
	return
}

func (s studentService) getStudentDetailBaseInfo(ctx *gin.Context, param *inputStudent.StudentDetailV1Param, arkData map[string]interface{}) (resp *outputStudent.StudentDetailV1Resp, err error) {
	resp = &outputStudent.StudentDetailV1Resp{
		Student: outputStudent.StudentInfo{},
	}

	// get dau base info
	studentInfoMap, err := dau.GetStudents(ctx, []int64{param.StudentUid}, nil)
	if err != nil {
		return
	}
	studentInfo, exists := studentInfoMap[param.CourseId]
	if !exists {
		err = fmt.Errorf("student not found,%v", param.StudentUid)
		return
	}
	addressInfo := s.getAddressInfo(ctx, param.StudentUid, param.CourseId)
	info, err := s.initStudentBaseInfo(ctx, studentInfo, addressInfo, param, arkData)
	if err != nil {
		return
	}
	resp.Student = *info
	// get other base info
	assistantInfo, err := mesh.NewClient().GetUserInfoByDeviceUid(ctx, param.AssistantUid)
	if err != nil {
		return
	}
	resp.ContactFlagDefault = cast.ToString(arkData["contactFlag"])
	resp.ContactFlagTime = cast.ToString(arkData["contactFlagTime"])
	resp.AssistantUid = param.AssistantUid
	resp.AssistantPhone = util.MaskPhone(assistantInfo.Record.Phone)
	resp.IsLevelTwo = cast.ToInt(arkData["isLevelTwo"])

	return
}

func (s studentService) initStudentBaseInfo(ctx *gin.Context, info *dau.StudentInfo, addressInfo moat.DetailAddressInfo, param *inputStudent.StudentDetailV1Param, arkData map[string]interface{}) (resp *outputStudent.StudentInfo, err error) {
	// address
	address := ""
	if info.Area == "" && (addressInfo.Province != "" && addressInfo.City != "") {
		address = addressInfo.Province + addressInfo.City
	}

	// EncryptedStudentUid
	encUid, err := utils.EncodeUid(int(param.StudentUid))
	if err != nil {
		zlog.Warnf(ctx, "initStudentBaseInfo EncodeUid failed, err:%v", err)
	}
	phonesToEncode := []string{
		addressInfo.Phone,
		info.GuardianPhone,
		info.Phone,
		info.RegisterPhone,
	}
	encodedPhones, err := util.EncodePhones(phonesToEncode...)
	if err != nil {
		zlog.Warnf(ctx, "encodePhones err: %v", err)
	}

	// pinyin
	pinYin, err := assistantdeskgo.GetPinYin(ctx, assistantdeskgo.GetPinYinReq{ChineseName: []string{info.StudentName}})
	if err != nil {
		zlog.Warnf(ctx, "initStudentBaseInfo GetPinYin failed, err:%v", err)
	}

	pinYinRes := assistantdeskgo.GetPingYinItem{}
	if pinYin != nil {
		for _, item := range pinYin.PingYin {
			if item.Name == info.StudentName {
				pinYinRes = item
			}
		}
	}

	resp = &outputStudent.StudentInfo{
		StudentUid:          param.StudentUid,
		LeadsId:             param.LeadsId,
		EncryptedStudentUid: encUid,
		StudentName:         info.StudentName,
		Nickname:            info.Uname,
		Sex:                 info.Sex,
		Avatar:              info.Avatar,
		Grade:               cast.ToString(info.Grade),
		Phone:               info.Phone,
		Address:             address,
		Guardian:            info.Guardian,
		GuardianPhone:       info.GuardianPhone,
		GuardianWechat:      info.ParentWebchat,
		RegPhone:            info.RegisterPhone,
		RegTime:             info.RegTime,
		Area:                info.Area,
		School:              info.School,
		ClassOL:             "", // 没用到
		GuardianWechatLight: cast.ToInt(arkData["guardianWechatLight"]),
		AddressPhone:        addressInfo.Phone,
		ServiceType:         cast.ToInt(arkData["serviceType"]),
		ClassId:             cast.ToInt(arkData["classId"]),
		ScRemark:            cast.ToString(arkData["scRemark"]),
		PreContinue:         cast.ToInt(arkData["preContinue"]),
		MachinePreContinue:  cast.ToInt(arkData["machinePreContinue"]),
		GradeData:           define.GetGradeStage()[info.Grade],
		EncryCustomUid:      encUid,
		Md5AddressPhone:     encodedPhones[0],
		Md5GuardianPhone:    encodedPhones[1],
		Md5Phone:            encodedPhones[2],
		Md5RegPhone:         encodedPhones[3],
		StudentNamePinYin:   pinYinRes,
	}

	return
}
