package studentTagDefine

import (
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/output/outputStudent"
)

// GetTagStyle 根据标签名返回对应的 Tag 样式
func GetTagStyle(tagName string) outputStudent.Tag {
	switch tagName {
	case consts.TagDemo:
		return DetailTagDefault
	default:
		return DetailTagDefault // 默认返回值
	}
}

var TagUserTypeStyleMap = map[string]map[string]outputStudent.Tag{
	consts.TagUserType:    DetailTagUserType,    // 用户类型
	consts.TagLpcUserType: DetailTagLpcUserType, // Lpc用户类型
}

var TagDIYStyleMap = map[string]struct{}{
	consts.TagUserType:            {},
	consts.TagLpcUserType:         {},
	consts.TagIsBound:             {},
	consts.TagIsLevelTwo:          {},
	consts.TagIsTransferCourse:    {},
	consts.TagIsTransferStudent:   {},
	consts.TagSingleApplyRetain:   {},
	consts.TagIsTransferStudentB:  {},
	consts.TagDelaySale:           {},
	consts.TagGuardianWechatLight: {},
	consts.TagServiceType:         {},
	consts.TagChannel:             {},
	consts.TagLpcNum:              {},
	consts.TagLpcLeadsDeploy:      {},
	consts.TagLpcSecondChannel:    {},
	consts.TagLpcCityLevel:        {},
	consts.TagLpcTransferStatus:   {},
	consts.TagLpcUserDelamination: {},
	consts.TagLpcChannel:          {},
}
