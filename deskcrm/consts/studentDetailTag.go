package consts

const (
	// 用户类型常量
	TypePreNew      = "1" // 新用户
	TypePrivateLong = "2" // 班课
	TypeOther       = "3" // 其他

	// 标签颜色常量
	TagColorGreen  = "green"
	TagColorGray   = "gray"
	TagColorPurple = "purple"
	TagColorBlue   = "blue"
	TagColorOrange = "orange"
	TagColorYellow = "yellow"

	// 标签类型常量
	TagUserType            = "tagUserType"            // 用户类型
	TagIsBound             = "tagIsBound"             // 是否联报
	TagIsLevelTwo          = "tagIsLevelTwo"          // 是否续报
	TagIsTransferCourse    = "tagIsTransferCourse"    // 是否转班
	TagIsTransferStudent   = "tagIsTransferStudent"   // 是否插班
	TagSingleApplyRetain   = "tagSingleApplyRetain"   // 单报留存
	TagIsTransferStudentB  = "tagIsTransferStudentB"  // B类插班生
	TagDelaySale           = "tagDelaySale"           // 大促延期
	TagGuardianWechatLight = "tagGuardianWechatLight" // 微信添加状态
	TagServiceType         = "tagServiceType"         // 服务号关注状态
	TagLpcUserType         = "tagLpcUserType"         // Lpc用户类型
	TagChannel             = "tagChannel"             // Lpc渠道
	TagLpcNum              = "tagLpcNum"              // Lpc数量
	TagLpcLeadsDeploy      = "tagLpcLeadsDeploy"      // Lpc例子调配
	TagLpcSecondChannel    = "tagLpcSecondChannel"    // Lpc二级渠道
	TagLpcCityLevel        = "tagLpcCityLevel"        // Lpc城市级别
	TagLpcTransferStatus   = "tagLpcTransferStatus"   // Lpc转化状态
	TagLpcUserDelamination = "tagLpcUserDelamination" // Lpc用户分层
	TagLpcChannel          = "tagLpcChannel"          // Lpc购课渠道
	TagDemo                = "tagDemo"                //
)

// 例子渠道(new)
const (
	LeadsFirstChannelIDIn      = 1
	LeadsFirstChannelIDOut     = 2
	LeadsFirstChannelIDAppoint = 3
	LeadsFirstChannelIDWechat  = 4
	LeadsFirstChannelIDOL      = 5
	LeadsFirstChannelIDSpecial = 6
)

// LeadsFirstChannelIDMap 将渠道ID映射到渠道名称
var LeadsFirstChannelIDMap = map[int]string{
	LeadsFirstChannelIDIn:      "端内购课",
	LeadsFirstChannelIDOut:     "端外投放",
	LeadsFirstChannelIDAppoint: "LEC约课",
	LeadsFirstChannelIDWechat:  "微信生态",
	LeadsFirstChannelIDOL:      "OL",
	LeadsFirstChannelIDSpecial: "其他项目",
}

var LeadsSourceMap = map[int]string{
	1: "端内",
	2: "端外",
	3: "LEC约课",
	4: "微信",
	5: "OL",
	6: "其他渠道",
}
